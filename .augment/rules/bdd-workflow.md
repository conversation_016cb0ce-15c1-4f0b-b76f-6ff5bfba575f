---
type: "always_apply"
---

# BDD Workflow Rules - Generic

## CRITICAL: Always Follow BDD Order
You MUST follow this exact sequence for every feature request:

1. **FIRST**: Write behavior specifications in natural language
2. **SECOND**: Write failing tests that verify these behaviors
3. **THIRD**: Write minimal production code to pass tests
4. **FOURTH**: Refactor while keeping tests green

**NEVER write production code before behavior specs and tests are complete.**

## Behavior Specification Requirements

### Every Feature Must Start With Natural Language Specs:
```
Feature: [Clear, business-focused feature name]
Purpose: [Why this feature exists and what business value it provides]
User: [Who will use this feature]

Behavior 1: [Main success scenario]
- Given: [initial context/preconditions]
- When: [specific action/event]
- Then: [expected outcome/result]

Behavior 2: [Alternative scenario]
- Given: [different context]
- When: [action under different conditions]
- Then: [different expected outcome]

Behavior 3: [Error scenario]
- Given: [error-prone context]
- When: [action that should fail]
- Then: [appropriate error response]
```

### Behavior Quality Checklist:
- [ ] Uses business language, not technical implementation details
- [ ] Each behavior tests one specific outcome
- [ ] Covers happy path, alternative flows, and error cases
- [ ] Includes edge cases and boundary conditions
- [ ] Uses concrete examples with real data
- [ ] Focuses on user outcomes, not system internals

### Required Behavior Coverage:
1. **Happy Path**: Main success scenario with valid inputs
2. **Alternative Flows**: Valid variations in user behavior
3. **Error Handling**: Invalid inputs, system failures, edge conditions
4. **Boundary Testing**: Min/max values, limits, thresholds

## Test Implementation Rules

### After Behavior Specs Are Complete:
1. Write test code that verifies each specified behavior
2. Use descriptive test names that match behavior intent
3. Ensure tests fail initially (Red phase)
4. Test behavior, not implementation details
5. Use realistic test data that matches specifications

### Test Structure Standards:
- Group tests by feature or component
- Use descriptive test names that explain expected behavior
- Follow Arrange-Act-Assert pattern
- Keep tests independent and deterministic
- Clean up test state between runs

### Example Test Structure (language-agnostic):
```
describe "Feature: User Authentication"
  describe "valid user login"
    test "allows user to login with correct credentials"
      # Arrange: Set up test user with valid credentials
      # Act: Attempt login with those credentials
      # Assert: Login succeeds and returns expected result

  describe "invalid user login"
    test "rejects login with incorrect password"
      # Arrange: Set up test user
      # Act: Attempt login with wrong password
      # Assert: Login fails with appropriate error
```

## Production Code Rules

### Only After Tests Are Written and Failing:
1. Write minimal code to make the first failing test pass
2. Don't add functionality not covered by tests
3. Keep implementation simple - avoid premature optimization
4. Make tests pass with the simplest possible solution

### Red-Green-Refactor Cycle:
1. **Red**: Write failing test for next behavior
2. **Green**: Write minimal code to pass the test
3. **Refactor**: Clean up code while keeping tests green
4. **Repeat**: Move to next behavior specification

## Implementation Standards

### Code Quality Requirements:
- Single responsibility: Each function does one thing
- Pure functions preferred: Predictable input/output
- Meaningful names: Variables and functions explain their purpose
- Error handling: Handle all error conditions explicitly
- No magic numbers: Use named constants

### Anti-Patterns to Avoid:
**NEVER:**
- Write production code before behavior specifications
- Skip the failing test phase
- Write tests after implementation
- Test implementation details instead of behavior
- Use technical jargon in behavior descriptions
- Combine multiple behaviors in one test

## Validation Checklist

Before considering any feature complete:
- [ ] All behaviors are specified in natural language
- [ ] Tests exist for every specified behavior
- [ ] All tests initially failed (Red phase)
- [ ] Production code makes all tests pass (Green phase)
- [ ] Code has been refactored for clarity
- [ ] No production code exists without corresponding test coverage
- [ ] All behaviors work as specified

## Response Format

### When Presenting BDD Work:
Always show in this order:
1. **Behavior Specifications**: Natural language behavior descriptions
2. **Test Implementation**: Test code that verifies behaviors
3. **Production Code**: Minimal implementation to pass tests
4. **Refactored Solution**: Clean, final implementation

### Example Response Structure:
```
## Feature: [Name]

### Behavior Specifications:
[Natural language behavior descriptions]

### Test Implementation:
[Test code in appropriate testing framework]

### Production Implementation:
[Minimal code to pass tests]

### Refactored Solution:
[Clean, maintainable final code]
```

This ensures strict adherence to BDD principles while remaining tool and language agnostic.
