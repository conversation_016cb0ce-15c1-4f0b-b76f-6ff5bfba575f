---
type: "always_apply"
---

# Code Quality Standards - Generic

## Code Organization Principles

### Structure and Clarity:
- **Single Responsibility**: Each function/module does one thing well
- **Clear Naming**: Names explain purpose without needing comments
- **Consistent Style**: Follow language-specific conventions
- **Logical Organization**: Group related functionality together
- **Minimal Dependencies**: Reduce coupling between components

### Function Standards:
- **Pure functions preferred**: Predictable input/output, no side effects
- **Limited parameters**: Prefer fewer parameters, use objects/structs for complex input
- **Reasonable length**: Functions should be easy to understand at a glance
- **Single level of abstraction**: Don't mix high-level and low-level operations
- **Descriptive names**: Function names clearly explain what they do

## Error Handling Standards

### Error Handling Requirements:
- **Handle all error conditions** - Don't ignore potential failures
- **Use appropriate error types** - Create domain-specific error types
- **Fail fast** - Validate inputs early and clearly
- **Meaningful error messages** - Include context and actionable information
- **Consistent error patterns** - Use language idioms for error handling

### Error Handling Examples by Language:
```
// TypeScript/JavaScript: Use Result types or proper Error classes
// Ruby: Use exceptions and rescue blocks appropriately
// Elixir: Use {:ok, result} | {:error, reason} patterns
// Python: Use appropriate exception types and try/except blocks
```

### Input Validation:
- Validate all external inputs (user data, API calls, file contents)
- Use type systems to catch errors at compile time when possible
- Provide clear validation error messages
- Sanitize inputs to prevent security issues

## Code Quality Principles

### Readability Standards:
- **Self-documenting code**: Code should be readable without extensive comments
- **Consistent formatting**: Use language-standard formatters
- **Meaningful variable names**: Avoid abbreviations and single-letter variables
- **Logical flow**: Code should read like a narrative
- **Appropriate abstraction levels**: Don't over-engineer or under-engineer

### Performance Considerations:
- **Avoid premature optimization** - Write clear code first
- **Use appropriate data structures** - Choose right tool for the job
- **Profile before optimizing** - Measure actual performance bottlenecks
- **Consider algorithmic complexity** - Understand Big O implications
- **Cache expensive operations** when appropriate

### Security Best Practices:
- **Validate all inputs** - Never trust external data
- **Use parameterized queries** - Prevent injection attacks
- **Handle sensitive data carefully** - Don't log secrets or personal information
- **Follow principle of least privilege** - Only grant necessary permissions
- **Keep dependencies updated** - Regular security updates

## Documentation Standards

### Code Comments:
- **Document why, not what** - Code should show what it does
- **Complex algorithms**: Explain the approach and reasoning
- **Business rules**: Document domain-specific logic
- **API boundaries**: Document public interfaces thoroughly
- **Remove dead code**: Don't leave commented-out code

### Function Documentation:
```
# Language-agnostic documentation principles:
# - Document purpose and behavior
# - List parameters and their types/constraints
# - Describe return values and possible errors
# - Include usage examples for complex functions
# - Note any side effects or dependencies
```

## Refactoring Guidelines

### When to Refactor:
- **Code duplication** - Extract common functionality
- **Complex functions** - Break into smaller, focused functions
- **Unclear naming** - Improve variable and function names
- **Deep nesting** - Simplify conditional logic
- **Long parameter lists** - Use structured data types

### Safe Refactoring Process:
1. **Ensure test coverage** - Have tests before refactoring
2. **Make small changes** - One improvement at a time
3. **Run tests frequently** - After each small change
4. **Commit progress** - Save working states
5. **Verify behavior unchanged** - Tests should still pass

## Code Review Standards

### Before Submitting Code:
- [ ] All tests pass locally
- [ ] Code follows project style guidelines
- [ ] Error handling is comprehensive
- [ ] No obvious security issues
- [ ] Performance implications considered
- [ ] Code is readable and well-organized

### Code Review Focus Areas:
- **Correctness**: Does the code do what it's supposed to do?
- **Readability**: Is the code easy to understand and maintain?
- **Design**: Is the solution well-architected?
- **Testing**: Is the code properly tested?
- **Security**: Are there potential security vulnerabilities?
- **Performance**: Are there obvious performance issues?

## Language-Agnostic Quality Principles

### Universal Best Practices:
- **Consistent indentation and formatting**
- **Meaningful naming conventions**
- **Appropriate use of language idioms**
- **Clear separation of concerns**
- **Minimal cyclomatic complexity**

### Avoid These Anti-Patterns:
- **Magic numbers and strings** - Use named constants
- **Deep nesting** - Prefer early returns and guard clauses
- **God objects/functions** - Break large units into smaller ones
- **Tight coupling** - Depend on abstractions, not concrete implementations
- **Premature abstraction** - Don't generalize until you have multiple use cases

## Maintenance and Evolution

### Code Maintenance Principles:
- **Regular dependency updates** - Keep libraries current
- **Continuous refactoring** - Improve code continuously
- **Remove unused code** - Delete dead code and unused dependencies
- **Monitor code metrics** - Track complexity and technical debt
- **Document architectural decisions** - Record why choices were made

### Technical Debt Management:
- **Identify debt early** - Regular code quality assessments
- **Prioritize critical debt** - Fix issues that impact development velocity
- **Allocate time for improvements** - Include refactoring in planning
- **Track debt metrics** - Measure and monitor technical debt levels

## Quality Automation

### Automated Quality Checks:
- **Linting and formatting** - Enforce consistent style automatically
- **Type checking** - Use static analysis when available
- **Security scanning** - Automated vulnerability detection
- **Code complexity metrics** - Monitor cyclomatic complexity
- **Test coverage tracking** - Ensure adequate test coverage

### Integration with Development Workflow:
- **Pre-commit hooks** - Run quality checks before commits
- **CI pipeline validation** - Automated quality gates
- **Regular quality reports** - Track quality metrics over time
- **Fail fast on quality issues** - Don't merge code that fails quality standards
