---
type: "always_apply"
---

# Testing Standards - Generic

## Testing Strategy Requirements

### Test Coverage Standards:
- **High coverage** for critical business logic
- **Complete coverage** for public APIs and interfaces
- **Integration tests** for external dependencies
- **End-to-end tests** for critical user workflows

### Test Types Required:

#### Unit Tests
- Test individual functions/methods in isolation
- Mock external dependencies
- Fast execution
- Cover all edge cases and error conditions
- Follow Arrange-Act-Assert pattern

#### Integration Tests
- Test component interactions and data flow
- Test with real databases/services when possible
- Verify contract compliance between modules
- Test error propagation and handling

#### End-to-End Tests
- Test complete user workflows
- Use realistic test scenarios
- Cover critical business processes
- Include error recovery paths

## Test Organization Standards

### Test File Structure:
Organize tests by feature or module, not by test type:
```
tests/
â”œâ”€â”€ user-management/
â”‚   â”œâ”€â”€ authentication-test.*
â”‚   â”œâ”€â”€ profile-management-test.*
â”‚   â””â”€â”€ user-registration-test.*
â”œâ”€â”€ payment-processing/
â”‚   â”œâ”€â”€ payment-validation-test.*
â”‚   â””â”€â”€ transaction-handling-test.*
â””â”€â”€ shared/
    â”œâ”€â”€ utilities-test.*
    â””â”€â”€ validation-test.*
```

### Test Naming Standards:
- **Test files**: Match the module they test with appropriate suffix
- **Test descriptions**: Use descriptive, behavior-focused names
- **Test methods**: Explain what behavior is being verified

```
// Examples across different languages:
// JavaScript/TypeScript: describe("when user submits valid form", ...)
// Ruby: context "when user submits valid form" do
// Elixir: describe "when user submits valid form" do
// Python: class TestValidFormSubmission:
```

## Test Quality Standards

### Every Test Must:
1. **Have clear purpose** - What specific behavior is being verified
2. **Be independent** - Can run in isolation, any order
3. **Be deterministic** - Same input always produces same result
4. **Be fast** - Unit tests should complete quickly
5. **Be maintainable** - Easy to understand and modify

### Test Data Management:
- **Use factories or builders** for creating test objects
- **Clean up after tests** - Reset state between test runs
- **Use realistic data** - Representative of production scenarios
- **Avoid hardcoded values** - Use constants or generated data
- **Isolate test data** - Each test should have its own data

### Assertion Best Practices:
- Make specific assertions about expected behavior
- Assert on outcomes, not implementation details
- Use meaningful assertion messages
- One logical assertion per test (may include multiple assert statements)
- Verify both positive and negative cases

## Mocking and Test Doubles

### When to Use Test Doubles:
- **External services** - APIs, third-party libraries, network calls
- **Expensive operations** - Database queries, file I/O, complex calculations
- **Non-deterministic behavior** - Random values, current time, system state
- **Error simulation** - Force error conditions for testing

### Test Double Guidelines:
- Mock behavior, not data structures
- Verify interactions when relevant
- Use realistic mock responses
- Don't mock what you don't own (prefer integration tests)
- Keep mocks simple and focused

## Test Automation Requirements

### Continuous Integration:
- **All tests must pass** before code integration
- **Run tests on multiple environments** when applicable
- **Fast feedback** - Fail quickly on test failures
- **Parallel execution** when possible
- **Clear failure reporting** with actionable information

### Test Execution Standards:
```bash
# Standard test commands (adapt to your tools):
# Run all tests: npm test / bundle exec rspec / mix test / python -m pytest
# Run specific test file: [test-runner] path/to/test/file
# Run with coverage: [coverage-tool] [test-command]
# Run in watch mode: [test-runner] --watch
```

## Error Handling in Tests

### Test Error Scenarios:
- Invalid input validation
- Network failures and timeouts
- Database constraint violations
- Authentication and authorization failures
- Resource exhaustion scenarios

### Error Testing Patterns:
```
# Test that specific errors are raised
test "raises validation error for invalid email"
  # Arrange: Invalid email input
  # Act: Call validation function
  # Assert: Specific error type and message

# Test error recovery and cleanup
test "recovers gracefully from database connection failure"
  # Arrange: Mock database to fail
  # Act: Attempt database operation
  # Assert: Appropriate fallback behavior
```

## Performance Testing

### Performance Test Types:
- **Load testing** - Normal expected traffic
- **Stress testing** - Peak load scenarios
- **Volume testing** - Large data sets
- **Endurance testing** - Extended operation periods

### Performance Standards:
- Define acceptable response times for each operation type
- Test with realistic data volumes
- Monitor resource usage during tests
- Establish performance baselines and regression detection

## Test Documentation

### Test Documentation Requirements:
- **Clear test descriptions** that explain the behavior being tested
- **Setup instructions** for running tests locally
- **Troubleshooting guides** for common test failures
- **Test strategy explanation** - Why certain tests exist

## Test Maintenance

### Regular Test Maintenance:
- **Remove obsolete tests** when functionality changes
- **Update test data** to reflect current business rules
- **Refactor test code** to maintain readability
- **Fix flaky tests** immediately - don't ignore intermittent failures

### Test Code Quality:
- Apply same quality standards as production code
- Avoid duplication in test setup and assertions
- Extract common test utilities and helpers
- Keep test code simple and focused

## Anti-Patterns to Avoid

### Never:
- **Test implementation details** - Test behavior, not internal structure
- **Write tests after code** - Follow TDD/BDD practices
- **Use production data** - Always use dedicated test data
- **Skip test cleanup** - Always reset state between tests
- **Ignore flaky tests** - Fix or remove unreliable tests
- **Test multiple unrelated behaviors** - Keep tests focused

### Common Mistakes:
- Testing framework code instead of your code
- Overly complex test setups that obscure the test intent
- Assertions that don't actually verify the intended behavior
- Tests that depend on external systems without proper isolation
- Brittle tests that break with minor code changes
